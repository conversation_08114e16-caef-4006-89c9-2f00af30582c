/**
 * TC WEHEN - FORMS HANDLER
 * Contact Form Validation and Submission
 */

class FormHandler {
    constructor() {
        this.forms = document.querySelectorAll('form');
        this.init();
    }
    
    init() {
        this.forms.forEach(form => {
            this.setupForm(form);
        });
        
        console.log('Form handler initialized for', this.forms.length, 'forms');
    }
    
    setupForm(form) {
        // Add real-time validation
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });
        
        // Handle form submission
        form.addEventListener('submit', (e) => this.handleSubmit(e, form));
    }
    
    validateField(field) {
        const value = field.value.trim();
        const fieldName = field.name;
        const isRequired = field.hasAttribute('required');
        
        // Clear previous errors
        this.clearFieldError(field);
        
        // Required field validation
        if (isRequired && !value) {
            this.showFieldError(field, 'Dieses Feld ist erforderlich');
            return false;
        }
        
        // Email validation
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                this.showFieldError(field, 'Bitte geben Sie eine gültige E-Mail-Adresse ein');
                return false;
            }
        }
        
        // Phone validation (German format)
        if (field.type === 'tel' && value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{6,}$/;
            if (!phoneRegex.test(value)) {
                this.showFieldError(field, 'Bitte geben Sie eine gültige Telefonnummer ein');
                return false;
            }
        }
        
        // Name validation
        if (fieldName === 'name' && value) {
            if (value.length < 2) {
                this.showFieldError(field, 'Name muss mindestens 2 Zeichen lang sein');
                return false;
            }
        }
        
        // Message validation
        if (fieldName === 'message' && value) {
            if (value.length < 10) {
                this.showFieldError(field, 'Nachricht muss mindestens 10 Zeichen lang sein');
                return false;
            }
        }
        
        return true;
    }
    
    showFieldError(field, message) {
        field.classList.add('error');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        // Add new error message
        const errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        errorElement.textContent = message;
        field.parentNode.appendChild(errorElement);
    }
    
    clearFieldError(field) {
        field.classList.remove('error');
        const errorMessage = field.parentNode.querySelector('.error-message');
        if (errorMessage) {
            errorMessage.remove();
        }
    }
    
    validateForm(form) {
        const fields = form.querySelectorAll('input[required], textarea[required], select[required]');
        let isValid = true;
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    handleSubmit(e, form) {
        e.preventDefault();
        
        // Validate form
        if (!this.validateForm(form)) {
            this.showFormMessage(form, 'Bitte korrigieren Sie die Fehler im Formular', 'error');
            return;
        }
        
        // Show loading state
        this.setFormLoading(form, true);
        
        // Simulate form submission (replace with actual submission logic)
        this.submitForm(form);
    }
    
    async submitForm(form) {
        try {
            // Get form data
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            // Simulate API call (replace with actual endpoint)
            await this.simulateSubmission(data);
            
            // Success
            this.showFormMessage(form, 'Vielen Dank! Ihre Nachricht wurde erfolgreich gesendet. Wir melden uns bald bei Ihnen.', 'success');
            form.reset();
            
        } catch (error) {
            console.error('Form submission error:', error);
            this.showFormMessage(form, 'Es ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut oder kontaktieren Sie uns direkt.', 'error');
        } finally {
            this.setFormLoading(form, false);
        }
    }
    
    simulateSubmission(data) {
        return new Promise((resolve, reject) => {
            // Simulate network delay
            setTimeout(() => {
                // Simulate random success/failure for demo
                if (Math.random() > 0.1) { // 90% success rate
                    console.log('Form data submitted:', data);
                    resolve();
                } else {
                    reject(new Error('Simulated network error'));
                }
            }, 2000);
        });
    }
    
    setFormLoading(form, isLoading) {
        const submitButton = form.querySelector('button[type="submit"]');
        const inputs = form.querySelectorAll('input, textarea, select, button');
        
        if (isLoading) {
            submitButton.textContent = 'Wird gesendet...';
            submitButton.disabled = true;
            inputs.forEach(input => input.disabled = true);
            form.classList.add('loading');
        } else {
            submitButton.textContent = 'Nachricht senden';
            submitButton.disabled = false;
            inputs.forEach(input => input.disabled = false);
            form.classList.remove('loading');
        }
    }
    
    showFormMessage(form, message, type) {
        // Remove existing message
        const existingMessage = form.querySelector('.form-message');
        if (existingMessage) {
            existingMessage.remove();
        }
        
        // Create new message
        const messageElement = document.createElement('div');
        messageElement.className = `form-message ${type}`;
        messageElement.textContent = message;
        
        // Insert at the top of the form
        form.insertBefore(messageElement, form.firstChild);
        
        // Auto-remove success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.remove();
                }
            }, 5000);
        }
        
        // Scroll to message
        messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

// Initialize form handler when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new FormHandler();
});

// Add CSS for form validation styles
const formStyles = `
    <style>
        .form-group input.error,
        .form-group textarea.error,
        .form-group select.error {
            border-color: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
        }
        
        .error-message {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: block;
        }
        
        .form-message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }
        
        .form-message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .form-message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .contact-form.loading {
            opacity: 0.7;
            pointer-events: none;
        }
        
        .contact-form.loading button[type="submit"] {
            position: relative;
        }
        
        .contact-form.loading button[type="submit"]::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Mobile form improvements */
        @media (max-width: 767px) {
            .form-group input,
            .form-group textarea,
            .form-group select {
                font-size: 16px; /* Prevent zoom on iOS */
            }
        }
    </style>
`;

// Inject styles
document.head.insertAdjacentHTML('beforeend', formStyles);

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FormHandler;
}
