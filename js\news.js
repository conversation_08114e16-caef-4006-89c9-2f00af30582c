/**
 * TC WEHEN - NEWS PAGE FUNCTIONALITY
 * Read More/Less functionality for news articles
 */

class NewsHandler {
    constructor() {
        this.newsItems = document.querySelectorAll('.news-item');
        this.init();
    }
    
    init() {
        this.setupReadMoreButtons();
        console.log('News handler initialized for', this.newsItems.length, 'news items');
    }
    
    setupReadMoreButtons() {
        this.newsItems.forEach(item => {
            const readMoreBtn = item.querySelector('.read-more-btn');
            const fullContent = item.querySelector('.news-full-content');
            
            if (readMoreBtn && fullContent) {
                readMoreBtn.addEventListener('click', () => {
                    this.toggleContent(item, readMoreBtn, fullContent);
                });
            }
        });
    }
    
    toggleContent(item, button, content) {
        const isExpanded = content.classList.contains('expanded');
        
        if (isExpanded) {
            // Collapse
            content.classList.remove('expanded');
            button.textContent = 'Weiterlesen';
            button.classList.remove('expanded');
            
            // Smooth scroll to article top
            item.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
            
        } else {
            // Expand
            content.classList.add('expanded');
            button.textContent = 'Weniger anzeigen';
            button.classList.add('expanded');
            
            // Add a small delay before scrolling to ensure content is expanded
            setTimeout(() => {
                // Scroll to show the full content
                const itemRect = item.getBoundingClientRect();
                const navbarHeight = document.querySelector('.navbar').offsetHeight;
                
                if (itemRect.bottom > window.innerHeight) {
                    window.scrollBy({
                        top: Math.min(300, itemRect.bottom - window.innerHeight + 50),
                        behavior: 'smooth'
                    });
                }
            }, 100);
        }
        
        // Trigger custom event
        this.dispatchToggleEvent(item, !isExpanded);
    }
    
    dispatchToggleEvent(item, isExpanded) {
        const event = new CustomEvent('newsToggle', {
            detail: {
                item: item,
                expanded: isExpanded,
                title: item.querySelector('h2').textContent
            }
        });
        document.dispatchEvent(event);
    }
    
    // Public API methods
    expandAll() {
        this.newsItems.forEach(item => {
            const readMoreBtn = item.querySelector('.read-more-btn');
            const fullContent = item.querySelector('.news-full-content');
            
            if (readMoreBtn && fullContent && !fullContent.classList.contains('expanded')) {
                this.toggleContent(item, readMoreBtn, fullContent);
            }
        });
    }
    
    collapseAll() {
        this.newsItems.forEach(item => {
            const readMoreBtn = item.querySelector('.read-more-btn');
            const fullContent = item.querySelector('.news-full-content');
            
            if (readMoreBtn && fullContent && fullContent.classList.contains('expanded')) {
                this.toggleContent(item, readMoreBtn, fullContent);
            }
        });
    }
    
    getExpandedItems() {
        return Array.from(this.newsItems).filter(item => {
            const fullContent = item.querySelector('.news-full-content');
            return fullContent && fullContent.classList.contains('expanded');
        });
    }
}

// Initialize news handler when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.newsHandler = new NewsHandler();
    
    // Optional: Listen to toggle events
    document.addEventListener('newsToggle', function(e) {
        console.log(`News article "${e.detail.title}" ${e.detail.expanded ? 'expanded' : 'collapsed'}`);
    });
});

// Add keyboard navigation for news items
document.addEventListener('keydown', function(e) {
    // Allow Enter or Space to trigger read more buttons
    if ((e.key === 'Enter' || e.key === ' ') && e.target.classList.contains('read-more-btn')) {
        e.preventDefault();
        e.target.click();
    }
});

// Add smooth scrolling for news navigation
function scrollToNews(newsIndex) {
    const newsItems = document.querySelectorAll('.news-item');
    if (newsItems[newsIndex]) {
        const navbarHeight = document.querySelector('.navbar').offsetHeight;
        const targetPosition = newsItems[newsIndex].offsetTop - navbarHeight - 20;
        
        window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
        });
    }
}

// Add search functionality (basic)
function searchNews(searchTerm) {
    const newsItems = document.querySelectorAll('.news-item');
    const searchLower = searchTerm.toLowerCase();
    let visibleCount = 0;
    
    newsItems.forEach(item => {
        const title = item.querySelector('h2').textContent.toLowerCase();
        const excerpt = item.querySelector('.news-excerpt').textContent.toLowerCase();
        const fullContent = item.querySelector('.news-full-content').textContent.toLowerCase();
        
        const isMatch = title.includes(searchLower) || 
                       excerpt.includes(searchLower) || 
                       fullContent.includes(searchLower);
        
        if (isMatch || searchTerm === '') {
            item.style.display = 'block';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });
    
    return visibleCount;
}

// Add category filtering
function filterByCategory(category) {
    const newsItems = document.querySelectorAll('.news-item');
    let visibleCount = 0;
    
    newsItems.forEach(item => {
        const itemCategory = item.querySelector('.news-category').textContent;
        
        if (category === 'all' || itemCategory === category) {
            item.style.display = 'block';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });
    
    return visibleCount;
}

// Add print functionality for individual news items
function printNewsItem(newsItem) {
    const title = newsItem.querySelector('h2').textContent;
    const date = newsItem.querySelector('.news-date').textContent;
    const excerpt = newsItem.querySelector('.news-excerpt').textContent;
    const fullContent = newsItem.querySelector('.news-full-content').innerHTML;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>${title} - TC Wehen</title>
            <style>
                body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
                h1 { color: #2d5016; border-bottom: 2px solid #2d5016; padding-bottom: 10px; }
                .date { color: #666; font-style: italic; margin-bottom: 20px; }
                .excerpt { font-size: 1.1em; font-weight: bold; margin-bottom: 20px; }
                .content { line-height: 1.6; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <h1>${title}</h1>
            <div class="date">${date}</div>
            <div class="excerpt">${excerpt}</div>
            <div class="content">${fullContent}</div>
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { NewsHandler, searchNews, filterByCategory, printNewsItem };
}
