/**
 * TC WEHEN - HERO SLIDER
 * Premium Image Slider for Hero Section
 */

class HeroSlider {
    constructor() {
        this.slides = document.querySelectorAll('.hero-slide');
        this.dots = document.querySelectorAll('.hero-dot');
        this.currentSlide = 0;
        this.slideInterval = null;
        this.autoplayDelay = 5000; // 5 seconds
        this.isPlaying = true;
        
        this.init();
    }
    
    init() {
        if (this.slides.length === 0) return;
        
        this.setupEventListeners();
        this.startAutoplay();
        this.preloadImages();
        
        console.log('Hero slider initialized with', this.slides.length, 'slides');
    }
    
    setupEventListeners() {
        // Dot navigation
        this.dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                this.goToSlide(index);
                this.resetAutoplay();
            });
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                this.previousSlide();
                this.resetAutoplay();
            } else if (e.key === 'ArrowRight') {
                this.nextSlide();
                this.resetAutoplay();
            }
        });
        
        // Touch/swipe support
        this.setupTouchEvents();
        
        // Pause on hover (desktop only)
        const heroSection = document.querySelector('.hero');
        if (heroSection && window.innerWidth > 768) {
            heroSection.addEventListener('mouseenter', () => this.pauseAutoplay());
            heroSection.addEventListener('mouseleave', () => this.resumeAutoplay());
        }
        
        // Pause when tab is not visible
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAutoplay();
            } else {
                this.resumeAutoplay();
            }
        });
    }
    
    setupTouchEvents() {
        const heroSection = document.querySelector('.hero');
        if (!heroSection) return;
        
        let startX = 0;
        let startY = 0;
        let endX = 0;
        let endY = 0;
        
        heroSection.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        }, { passive: true });
        
        heroSection.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            endY = e.changedTouches[0].clientY;
            
            const deltaX = endX - startX;
            const deltaY = endY - startY;
            
            // Check if horizontal swipe is more significant than vertical
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0) {
                    this.previousSlide();
                } else {
                    this.nextSlide();
                }
                this.resetAutoplay();
            }
        }, { passive: true });
    }
    
    goToSlide(index) {
        if (index < 0 || index >= this.slides.length) return;
        
        // Remove active class from current slide and dot
        this.slides[this.currentSlide].classList.remove('active');
        this.dots[this.currentSlide].classList.remove('active');
        
        // Set new current slide
        this.currentSlide = index;
        
        // Add active class to new slide and dot
        this.slides[this.currentSlide].classList.add('active');
        this.dots[this.currentSlide].classList.add('active');
        
        // Trigger custom event
        this.dispatchSlideChangeEvent();
    }
    
    nextSlide() {
        const nextIndex = (this.currentSlide + 1) % this.slides.length;
        this.goToSlide(nextIndex);
    }
    
    previousSlide() {
        const prevIndex = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
        this.goToSlide(prevIndex);
    }
    
    startAutoplay() {
        if (this.slides.length <= 1) return;
        
        this.slideInterval = setInterval(() => {
            this.nextSlide();
        }, this.autoplayDelay);
        
        this.isPlaying = true;
    }
    
    pauseAutoplay() {
        if (this.slideInterval) {
            clearInterval(this.slideInterval);
            this.slideInterval = null;
        }
        this.isPlaying = false;
    }
    
    resumeAutoplay() {
        if (!this.isPlaying && this.slides.length > 1) {
            this.startAutoplay();
        }
    }
    
    resetAutoplay() {
        this.pauseAutoplay();
        this.startAutoplay();
    }
    
    preloadImages() {
        this.slides.forEach((slide, index) => {
            const bgImage = slide.style.backgroundImage;
            if (bgImage) {
                const imageUrl = bgImage.slice(4, -1).replace(/"/g, "");
                const img = new Image();
                img.src = imageUrl;
                
                img.onload = () => {
                    console.log(`Hero image ${index + 1} preloaded`);
                };
                
                img.onerror = () => {
                    console.warn(`Failed to load hero image ${index + 1}:`, imageUrl);
                };
            }
        });
    }
    
    dispatchSlideChangeEvent() {
        const event = new CustomEvent('heroSlideChange', {
            detail: {
                currentSlide: this.currentSlide,
                totalSlides: this.slides.length
            }
        });
        document.dispatchEvent(event);
    }
    
    // Public API methods
    play() {
        this.resumeAutoplay();
    }
    
    pause() {
        this.pauseAutoplay();
    }
    
    getCurrentSlide() {
        return this.currentSlide;
    }
    
    getTotalSlides() {
        return this.slides.length;
    }
    
    destroy() {
        this.pauseAutoplay();
        // Remove event listeners if needed
        console.log('Hero slider destroyed');
    }
}

// Initialize hero slider when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Check if hero slider elements exist
    const heroSlides = document.querySelectorAll('.hero-slide');
    const heroDots = document.querySelectorAll('.hero-dot');
    
    if (heroSlides.length > 0 && heroDots.length > 0) {
        window.heroSlider = new HeroSlider();
        
        // Optional: Listen to slide change events
        document.addEventListener('heroSlideChange', function(e) {
            console.log(`Slide changed to ${e.detail.currentSlide + 1} of ${e.detail.totalSlides}`);
        });
    } else {
        console.log('Hero slider elements not found, skipping initialization');
    }
});

// Handle window resize
window.addEventListener('resize', function() {
    // Restart autoplay logic for mobile/desktop differences
    if (window.heroSlider) {
        window.heroSlider.resetAutoplay();
    }
});

// Accessibility improvements
document.addEventListener('keydown', function(e) {
    // Allow space bar to pause/play slider
    if (e.code === 'Space' && e.target === document.body) {
        e.preventDefault();
        if (window.heroSlider) {
            if (window.heroSlider.isPlaying) {
                window.heroSlider.pause();
            } else {
                window.heroSlider.play();
            }
        }
    }
});

// Performance optimization: Reduce autoplay frequency when page is not visible
document.addEventListener('visibilitychange', function() {
    if (window.heroSlider) {
        if (document.hidden) {
            window.heroSlider.pause();
        } else {
            // Resume after a short delay to avoid jarring transitions
            setTimeout(() => {
                window.heroSlider.play();
            }, 1000);
        }
    }
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HeroSlider;
}
