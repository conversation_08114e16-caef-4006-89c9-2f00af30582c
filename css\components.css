/* ===================================
   COMPONENT STYLES - TC WEHEN
   Premium Tennis Club Components
   =================================== */

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-light);
    z-index: 1000;
    transition: var(--transition-medium);
}

.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 20px var(--shadow-light);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-sm) 0;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding-left: var(--space-sm);
    padding-right: var(--space-sm);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.nav-logo {
    height: 40px;
    width: auto;
}

.nav-title {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--space-lg);
    margin: 0;
}

.nav-link {
    font-weight: 400;
    color: var(--text-dark);
    padding: var(--space-sm) var(--space-md);
    border-radius: 0;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link.active {
    color: var(--primary-color);
    background-color: transparent;
    border-bottom-color: var(--primary-color);
    font-weight: 500;
}



/* Mobile Navigation */
.nav-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--space-xs);
    gap: 4px;
}

.hamburger {
    width: 25px;
    height: 3px;
    background-color: var(--text-dark);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

/* Mobile Navigation Styles */
@media (max-width: 991px) {
    .nav-toggle {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--white);
        flex-direction: column;
        padding: var(--space-lg);
        box-shadow: 0 4px 20px var(--shadow-medium);
        transform: translateY(-100vh);
        transition: var(--transition-medium);
        gap: var(--space-sm);
    }
    
    .nav-menu.active {
        transform: translateY(0);
    }
    
    .nav-link {
        padding: var(--space-sm);
        text-align: center;
        border-radius: var(--radius-md);
        font-size: 1.125rem;
    }
    
    .nav-toggle.active .hamburger:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }
    
    .nav-toggle.active .hamburger:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active .hamburger:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.hero-slide.active {
    opacity: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.45) 0%,
        rgba(0, 0, 0, 0.35) 50%,
        rgba(0, 0, 0, 0.45) 100%
    );
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--white);
}

.hero-subtitle {
    display: block;
    font-size: 1.25rem;
    font-weight: 400;
    margin-bottom: var(--space-sm);
    color: var(--white);
    opacity: 0.9;
    text-shadow: none;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: var(--space-lg);
    color: var(--white);
    text-shadow: none;
}

.hero-description {
    font-size: 1.5rem;
    max-width: 500px;
    margin: 0 auto var(--space-xl);
    color: var(--white);
    opacity: 0.95;
    line-height: 1.5;
    text-shadow: none;
    font-weight: 400;
}

.hero-buttons {
    display: flex;
    gap: var(--space-md);
    justify-content: center;
    flex-wrap: wrap;
}

.hero-dots {
    position: absolute;
    bottom: var(--space-lg);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: var(--space-sm);
    z-index: 3;
}

.hero-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    border: none;
    cursor: pointer;
    transition: var(--transition-fast);
}

.hero-dot.active {
    background-color: var(--white);
    transform: scale(1.2);
}

/* Mobile Hero Adjustments */
@media (max-width: 767px) {
    .hero {
        height: 80vh;
        min-height: 500px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .hero-description {
        font-size: 1.25rem;
        padding: 0 var(--space-sm);
        line-height: 1.4;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--space-sm);
    }
    
    .hero-buttons .btn {
        width: 280px;
        max-width: 90%;
    }
}

/* ===== STATS SECTION ===== */
.stats {
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--space-xl) 0;
    margin-top: -1px; /* Overlap with hero */
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-lg);
    text-align: center;
}

.stat-item {
    padding: var(--space-md);
}

.stat-number {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--space-xs);
    color: var(--white);
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Mobile Stats */
@media (max-width: 767px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-md);
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

/* ===== ABOUT SECTION ===== */
.about {
    background-color: var(--white);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-xxl);
    align-items: center;
}

.about-text h3 {
    color: var(--primary-color);
    margin-bottom: var(--space-md);
}

.about-features {
    margin-top: var(--space-lg);
    display: grid;
    gap: var(--space-md);
}

.feature h4 {
    color: var(--text-dark);
    margin-bottom: var(--space-xs);
    font-size: 1.125rem;
}

.feature p {
    color: var(--text-light);
    margin-bottom: 0;
}

.about-image {
    position: relative;
}

.about-img {
    width: 100%;
    border-radius: var(--radius-lg);
    box-shadow: 0 8px 30px var(--shadow-medium);
}

.about-badge {
    position: absolute;
    bottom: var(--space-md);
    right: var(--space-md);
    background-color: var(--white);
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    box-shadow: 0 4px 15px var(--shadow-medium);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.htv-logo {
    height: 30px;
    width: auto;
}

.about-badge span {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.875rem;
}

/* Mobile About */
@media (max-width: 991px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
    }
    
    .about-image {
        order: -1;
    }
}

/* ===== FACILITIES SECTION ===== */
.facilities {
    background-color: var(--accent-color);
}

.facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
}

.facility-card {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: 0 4px 20px var(--shadow-light);
    transition: var(--transition-medium);
}

.facility-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px var(--shadow-medium);
}

.facility-image {
    height: 200px;
    overflow: hidden;
}

.facility-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.facility-card:hover .facility-image img {
    transform: scale(1.05);
}

.facility-content {
    padding: var(--space-lg);
}

.facility-content h3 {
    color: var(--primary-color);
    margin-bottom: var(--space-sm);
}

.facility-content p {
    color: var(--text-light);
    margin-bottom: 0;
}

/* Mobile Facilities */
@media (max-width: 767px) {
    .facilities-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }
}

/* ===== TENNIS SCHOOL SECTION ===== */
.tennis-school {
    background-color: var(--white);
}

.tennis-school-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--space-xxl);
    align-items: center;
}

.trainer-info {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: var(--space-xl);
    align-items: start;
}

.trainer-image {
    position: relative;
}

.trainer-photo {
    width: 200px;
    height: 200px;
    object-fit: cover;
    border-radius: 50%;
    border: 4px solid var(--primary-color);
    box-shadow: 0 8px 25px var(--shadow-medium);
}

.trainer-details h3 {
    color: var(--primary-color);
    margin-bottom: var(--space-md);
}

.training-options {
    margin-top: var(--space-lg);
    display: grid;
    gap: var(--space-md);
}

.training-option {
    padding: var(--space-md);
    background-color: var(--accent-color);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
}

.training-option h4 {
    color: var(--text-dark);
    margin-bottom: var(--space-xs);
}

.training-option p {
    margin-bottom: 0;
    color: var(--text-light);
}

.tennis-school-logo {
    text-align: center;
}

.school-logo {
    max-width: 200px;
    height: auto;
    filter: drop-shadow(0 4px 15px var(--shadow-light));
}

/* Mobile Tennis School */
@media (max-width: 991px) {
    .tennis-school-content {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
        text-align: center;
    }

    .trainer-info {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
        text-align: center;
    }

    .trainer-photo {
        width: 150px;
        height: 150px;
        margin: 0 auto;
    }
}

/* ===== NEWS SECTION ===== */
.news-preview {
    background-color: var(--background);
}

.news-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: var(--space-xl);
}

.news-card {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: 0 4px 20px var(--shadow-light);
    transition: var(--transition-medium);
}

.news-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px var(--shadow-medium);
}

.news-card.featured {
    grid-row: span 2;
}

.news-image {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.news-card.featured .news-image {
    height: 250px;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-category {
    position: absolute;
    top: var(--space-sm);
    left: var(--space-sm);
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 500;
}

.news-content {
    padding: var(--space-lg);
}

.news-date {
    color: var(--text-muted);
    font-size: 0.875rem;
    font-weight: 500;
}

.news-content h3 {
    color: var(--text-dark);
    margin: var(--space-sm) 0;
    font-size: 1.25rem;
}

.news-card.featured .news-content h3 {
    font-size: 1.5rem;
}

.news-content p {
    color: var(--text-light);
    margin-bottom: var(--space-md);
    line-height: 1.6;
}

.read-more {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-fast);
}

.read-more:hover {
    color: var(--primary-light);
}

.news-cta {
    text-align: center;
    margin-top: var(--space-xl);
}

/* Mobile News */
@media (max-width: 991px) {
    .news-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .news-card.featured {
        grid-row: span 1;
    }
}

/* ===== MEMBERSHIP SECTION ===== */
.membership {
    background-color: var(--white);
}

.membership-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-xxl);
    align-items: start;
}

.membership-info h3 {
    color: var(--primary-color);
    margin-bottom: var(--space-lg);
}

.membership-benefits {
    list-style: none;
    margin-bottom: var(--space-xl);
}

.membership-benefits li {
    padding: var(--space-sm) 0;
    color: var(--text-light);
    font-weight: 500;
    border-bottom: 1px solid var(--border-light);
}

.membership-benefits li:last-child {
    border-bottom: none;
}

.membership-types {
    display: grid;
    gap: var(--space-md);
}

.membership-type {
    padding: var(--space-md);
    background-color: var(--accent-color);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--secondary-color);
}

.membership-type h4 {
    color: var(--text-dark);
    margin-bottom: var(--space-xs);
}

.membership-type p {
    margin-bottom: 0;
    color: var(--text-light);
}

.membership-cta-card {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    padding: var(--space-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: 0 8px 30px var(--shadow-medium);
}

.membership-cta-card h3 {
    color: var(--white);
    margin-bottom: var(--space-md);
}

.membership-cta-card p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--space-lg);
}

.membership-cta-card .btn-primary {
    background-color: var(--white);
    color: var(--primary-color);
    border-color: var(--white);
}

.membership-cta-card .btn-primary:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.contact-quick {
    margin-top: var(--space-lg);
    padding-top: var(--space-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-quick p {
    margin-bottom: var(--space-xs);
    font-size: 0.875rem;
}

/* Mobile Membership */
@media (max-width: 991px) {
    .membership-content {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
    }
}

/* ===== CONTACT SECTION ===== */
.contact {
    background-color: var(--accent-color);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-xxl);
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-lg);
}

.contact-card {
    background-color: var(--white);
    padding: var(--space-lg);
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 15px var(--shadow-light);
    text-align: center;
    transition: var(--transition-medium);
}

.contact-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px var(--shadow-medium);
}

.contact-card h3 {
    color: var(--primary-color);
    margin-bottom: var(--space-md);
    font-size: 1.25rem;
}

.contact-card p {
    color: var(--text-light);
    margin-bottom: 0;
    line-height: 1.6;
}

.contact-form-container {
    background-color: var(--white);
    padding: var(--space-xl);
    border-radius: var(--radius-lg);
    box-shadow: 0 8px 30px var(--shadow-medium);
}

.contact-form h3 {
    color: var(--primary-color);
    margin-bottom: var(--space-lg);
    text-align: center;
}

.form-group {
    margin-bottom: var(--space-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--space-xs);
    font-weight: 600;
    color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--space-sm);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    font-family: var(--font-secondary);
    font-size: 1rem;
    transition: var(--transition-fast);
    background-color: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(45, 80, 22, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Mobile Contact */
@media (max-width: 991px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
    }

    .contact-info {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
}

/* ===== FOOTER ===== */
.footer {
    background-color: var(--text-dark);
    color: var(--white);
    padding: var(--space-xxl) 0 var(--space-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-xl);
    margin-bottom: var(--space-xl);
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: var(--space-md);
    font-size: 1.25rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-md);
}

.footer-logo img {
    height: 50px;
    width: auto;
}

.footer-logo h3 {
    color: var(--white);
    margin-bottom: 0;
    font-size: 1.5rem;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--space-xs);
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--white);
}

.partner-logos {
    display: flex;
    gap: var(--space-md);
    flex-wrap: wrap;
}

.partner-logo {
    height: 40px;
    width: auto;
    filter: brightness(0) invert(1);
    opacity: 0.8;
    transition: var(--transition-fast);
}

.partner-logo:hover {
    opacity: 1;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: var(--space-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-md);
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 0;
}

.footer-links {
    display: flex;
    gap: var(--space-lg);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--white);
}

/* Mobile Footer */
@media (max-width: 767px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
        gap: var(--space-sm);
    }

    .footer-links {
        justify-content: center;
    }

    .partner-logos {
        justify-content: center;
    }
}

/* ===== NEWS PAGE STYLES ===== */
.news-hero {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    padding: calc(80px + var(--space-xxl)) 0 var(--space-xxl);
    text-align: center;
}

.news-hero-title {
    font-size: 3rem;
    margin-bottom: var(--space-md);
    color: var(--white);
}

.news-hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.news-content {
    padding: var(--space-xxl) 0;
    background-color: var(--background);
}

.news-list {
    display: grid;
    gap: var(--space-xxl);
}

.news-item {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: 0 4px 20px var(--shadow-light);
    transition: var(--transition-medium);
}

.news-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px var(--shadow-medium);
}

.news-item-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.news-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.news-item:hover .news-item-image img {
    transform: scale(1.05);
}

.news-item-content {
    padding: var(--space-xl);
}

.news-item .news-date {
    color: var(--text-muted);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: var(--space-sm);
    display: block;
}

.news-item h2 {
    color: var(--text-dark);
    margin-bottom: var(--space-md);
    font-size: 1.75rem;
    line-height: 1.3;
}

.news-excerpt {
    color: var(--text-light);
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

.news-full-content {
    display: none;
    margin-top: var(--space-lg);
    padding-top: var(--space-lg);
    border-top: 1px solid var(--border-light);
}

.news-full-content.expanded {
    display: block;
}

.news-full-content p {
    margin-bottom: var(--space-md);
    line-height: 1.7;
}

.news-full-content ul {
    margin: var(--space-md) 0;
    padding-left: var(--space-lg);
}

.news-full-content li {
    margin-bottom: var(--space-xs);
    color: var(--text-light);
}

.read-more-btn {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    font-family: var(--font-secondary);
}

.read-more-btn:hover {
    background-color: var(--primary-light);
    transform: translateY(-1px);
}

.read-more-btn.expanded {
    background-color: var(--text-muted);
}

.read-more-btn.expanded:hover {
    background-color: var(--text-light);
}

/* Mobile News Styles */
@media (max-width: 767px) {
    .news-hero {
        padding: calc(70px + var(--space-xl)) 0 var(--space-xl);
    }

    .news-hero-title {
        font-size: 2.25rem;
    }

    .news-hero-subtitle {
        font-size: 1.125rem;
    }

    .news-item-image {
        height: 200px;
    }

    .news-item-content {
        padding: var(--space-lg);
    }

    .news-item h2 {
        font-size: 1.5rem;
    }

    .news-excerpt {
        font-size: 1rem;
    }
}
