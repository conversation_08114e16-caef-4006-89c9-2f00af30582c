/* ===================================
   TC WEHEN - PREMIUM TENNIS CLUB CSS
   Mobile-First Responsive Design
   =================================== */

/* CSS Custom Properties (Design System) */
:root {
    /* Colors - Inspired by tennis and nature */
    --primary-color: #2d5016;        /* Deep tennis green */
    --primary-light: #4a7c2a;       /* Lighter green */
    --primary-dark: #1a3009;        /* Darker green */
    --secondary-color: #c8a882;      /* Tennis court clay */
    --accent-color: #f4f1eb;         /* Cream white */
    --text-dark: #2c2c2c;           /* Almost black */
    --text-light: #666666;          /* Medium gray */
    --text-muted: #999999;          /* Light gray */
    --white: #ffffff;
    --background: #fafafa;           /* Off-white background */
    --border-light: #e5e5e5;        /* Light border */
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.15);
    --shadow-strong: rgba(0, 0, 0, 0.25);
    
    /* Typography */
    --font-primary: 'Playfair Display', serif;  /* Elegant headings */
    --font-secondary: 'Inter', sans-serif;      /* Clean body text */
    
    /* Spacing System */
    --space-xs: 0.5rem;    /* 8px */
    --space-sm: 1rem;      /* 16px */
    --space-md: 1.5rem;    /* 24px */
    --space-lg: 2rem;      /* 32px */
    --space-xl: 3rem;      /* 48px */
    --space-xxl: 4rem;     /* 64px */
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 20px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Container Sizes */
    --container-sm: 540px;
    --container-md: 720px;
    --container-lg: 960px;
    --container-xl: 1140px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-secondary);
    font-weight: 400;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--background);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--space-sm);
    color: var(--text-dark);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: var(--space-sm);
    color: var(--text-light);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-light);
}

/* Container System */
.container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--space-sm);
}

/* Mobile-first responsive containers */
@media (min-width: 576px) {
    .container { max-width: var(--container-sm); }
}

@media (min-width: 768px) {
    .container { 
        max-width: var(--container-md);
        padding: 0 var(--space-md);
    }
}

@media (min-width: 992px) {
    .container { max-width: var(--container-lg); }
}

@media (min-width: 1200px) {
    .container { max-width: var(--container-xl); }
}

/* Button System */
.btn {
    display: inline-block;
    padding: var(--space-sm) var(--space-lg);
    font-family: var(--font-secondary);
    font-weight: 500;
    font-size: 1rem;
    text-align: center;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-medium);
    min-height: 44px; /* Touch-friendly */
    line-height: 1.2;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-light);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-medium);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-medium);
}

/* Section Styling */
section {
    padding: var(--space-xxl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-xxl);
}

.section-title {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: var(--space-sm);
    position: relative;
}

.section-title::after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
    margin: var(--space-sm) auto;
    border-radius: var(--radius-sm);
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* Mobile Typography Adjustments */
@media (max-width: 767px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    .section-title { font-size: 2rem; }
    
    .btn {
        padding: var(--space-md) var(--space-lg);
        font-size: 1rem;
        width: 100%;
        margin-bottom: var(--space-sm);
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-xs); }
.mb-2 { margin-bottom: var(--space-sm); }
.mb-3 { margin-bottom: var(--space-md); }
.mb-4 { margin-bottom: var(--space-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-xs); }
.mt-2 { margin-top: var(--space-sm); }
.mt-3 { margin-top: var(--space-md); }
.mt-4 { margin-top: var(--space-lg); }

/* Hide on mobile */
@media (max-width: 767px) {
    .d-none-mobile { display: none !important; }
}

/* Hide on desktop */
@media (min-width: 768px) {
    .d-none-desktop { display: none !important; }
}

/* Responsive Images */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Focus States for Accessibility */
button:focus,
input:focus,
textarea:focus,
select:focus,
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Loading Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth Scrolling Enhancement */
@media (prefers-reduced-motion: no-preference) {
    html {
        scroll-behavior: smooth;
    }
}

/* Print Styles */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a, a:visited {
        text-decoration: underline;
    }
    
    .navbar, .footer, .btn {
        display: none;
    }
}
